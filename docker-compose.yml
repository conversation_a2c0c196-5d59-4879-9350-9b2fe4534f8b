# version: '3.8'  # Убрано для совместимости с новыми версиями Docker Compose

services:
  postgres:
    image: postgres:15-alpine
    container_name: telebot_postgres
    env_file:
      - /etc/edu_telebot/env
    volumes:
      - postgres_data:/var/lib/postgresql/data
    # ports:
    #   - "5432:5432"  # Закрыт для безопасности, доступ только внутри сети
    restart: unless-stopped
    networks:
      - telebot_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U telebot_user -d telebot"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    container_name: telebot_redis
    env_file:
      - /etc/edu_telebot/env
    volumes:
      - redis_data:/data
    # ports:
    #   - "6379:6379"  # Закрыт для безопасности, доступ только внутри сети
    restart: unless-stopped
    networks:
      - telebot_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    command: redis-server --appendonly yes ${REDIS_PASSWORD:+--requirepass $REDIS_PASSWORD}

  nginx:
    image: nginx:alpine
    container_name: telebot_nginx
    env_file:
      - /etc/edu_telebot/env
    # Продакшен порты - не конфликтуют с основным nginx
    ports:
      - "80:80"   # HTTP доступ через :8080
      - "443:443"  # HTTPS доступ через :8443
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf.template:ro
      - ./nginx/ssl:/etc/ssl:ro
      - ./logs/nginx:/var/log/nginx
    command: /bin/sh -c "envsubst '$$DOMAIN' < /etc/nginx/nginx.conf.template > /etc/nginx/nginx.conf && nginx -g 'daemon off;'"
    depends_on:
      - bot
    restart: unless-stopped
    networks:
      - telebot_network

  bot:
    build: .
    container_name: telebot_app
    env_file:
      - /etc/edu_telebot/env  # Все переменные берутся из безопасного файла
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - telebot_network
    volumes:
      - ./logs:/app/logs
    expose:
      - "8000"
    dns:
      - *******
      - *******
    extra_hosts:
      - "edubot.schoolpro.kz:*************"
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:8000/health" ]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:
  redis_data:

networks:
  telebot_network:
    driver: bridge
